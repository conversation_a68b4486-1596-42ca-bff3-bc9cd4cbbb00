// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// hajiNewsTagLanguagesDao is the data access object for the table haji_news_tag_languages.
// You can define custom methods on it to extend its functionality as needed.
type hajiNewsTagLanguagesDao struct {
	*internal.HajiNewsTagLanguagesDao
}

var (
	// HajiNewsTagLanguages is a globally accessible object for table haji_news_tag_languages operations.
	HajiNewsTagLanguages = hajiNewsTagLanguagesDao{internal.NewHajiNewsTagLanguagesDao()}
)

// Add your custom methods and functionality below.
