// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FaqCateDao is the data access object for the table faq_cate.
type FaqCateDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  FaqCateColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// FaqCateColumns defines and stores column names for the table faq_cate.
type FaqCateColumns struct {
	Id            string //
	IsOpen        string // 状态 [ 1 启用  2 禁用]
	Sort          string // 排序
	CateCount     string // 分类下的文章总数
	CreateAccount string // 创建者
	UpdateAccount string // 更新者
	CreateTime    string //
	UpdateTime    string //
	DeleteTime    string //
}

// faqCateColumns holds the columns for the table faq_cate.
var faqCateColumns = FaqCateColumns{
	Id:            "id",
	IsOpen:        "is_open",
	Sort:          "sort",
	CateCount:     "cate_count",
	CreateAccount: "create_account",
	UpdateAccount: "update_account",
	CreateTime:    "create_time",
	UpdateTime:    "update_time",
	DeleteTime:    "delete_time",
}

// NewFaqCateDao creates and returns a new DAO object for table data access.
func NewFaqCateDao(handlers ...gdb.ModelHandler) *FaqCateDao {
	return &FaqCateDao{
		group:    "islamic_content_svc",
		table:    "faq_cate",
		columns:  faqCateColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *FaqCateDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *FaqCateDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *FaqCateDao) Columns() FaqCateColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *FaqCateDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *FaqCateDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *FaqCateDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
