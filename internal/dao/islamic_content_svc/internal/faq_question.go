// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FaqQuestionDao is the data access object for the table faq_question.
type FaqQuestionDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  FaqQuestionColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// FaqQuestionColumns defines and stores column names for the table faq_question.
type FaqQuestionColumns struct {
	Id            string //
	FaqCateId     string //
	IsOpen        string // 状态 [ 1 启用  2 禁用]
	Sort          string // 排序
	PublishTime   string // 发布时间
	Views         string // 浏览量
	CreateAccount string // 创建者
	UpdateAccount string // 更新者
	CreateTime    string //
	UpdateTime    string //
	DeleteTime    string //
	IsZh          string // 是否中文，0-否，1-是
	IsEn          string // 是否英文，0-否，1-是
	IsId          string // 是否印尼文，0-否，1-是
}

// faqQuestionColumns holds the columns for the table faq_question.
var faqQuestionColumns = FaqQuestionColumns{
	Id:            "id",
	FaqCateId:     "faq_cate_id",
	IsOpen:        "is_open",
	Sort:          "sort",
	PublishTime:   "publish_time",
	Views:         "views",
	CreateAccount: "create_account",
	UpdateAccount: "update_account",
	CreateTime:    "create_time",
	UpdateTime:    "update_time",
	DeleteTime:    "delete_time",
	IsZh:          "is_zh",
	IsEn:          "is_en",
	IsId:          "is_id",
}

// NewFaqQuestionDao creates and returns a new DAO object for table data access.
func NewFaqQuestionDao(handlers ...gdb.ModelHandler) *FaqQuestionDao {
	return &FaqQuestionDao{
		group:    "islamic_content_svc",
		table:    "faq_question",
		columns:  faqQuestionColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *FaqQuestionDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *FaqQuestionDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *FaqQuestionDao) Columns() FaqQuestionColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *FaqQuestionDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *FaqQuestionDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *FaqQuestionDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
