// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HajiNewsTagLanguagesDao is the data access object for the table haji_news_tag_languages.
type HajiNewsTagLanguagesDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  HajiNewsTagLanguagesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// HajiNewsTagLanguagesColumns defines and stores column names for the table haji_news_tag_languages.
type HajiNewsTagLanguagesColumns struct {
	Id         string // 主键ID
	TagId      string // 标签ID，关联haji_news_tag.id
	LanguageId string // 语言ID：0-中文，1-英文，2-印尼语
	TagName    string // 标签名称
	CreateTime string // 创建时间（毫秒时间戳）
	UpdateTime string // 更新时间（毫秒时间戳）
}

// hajiNewsTagLanguagesColumns holds the columns for the table haji_news_tag_languages.
var hajiNewsTagLanguagesColumns = HajiNewsTagLanguagesColumns{
	Id:         "id",
	TagId:      "tag_id",
	LanguageId: "language_id",
	TagName:    "tag_name",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewHajiNewsTagLanguagesDao creates and returns a new DAO object for table data access.
func NewHajiNewsTagLanguagesDao(handlers ...gdb.ModelHandler) *HajiNewsTagLanguagesDao {
	return &HajiNewsTagLanguagesDao{
		group:    "islamic_content_svc",
		table:    "haji_news_tag_languages",
		columns:  hajiNewsTagLanguagesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HajiNewsTagLanguagesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HajiNewsTagLanguagesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HajiNewsTagLanguagesDao) Columns() HajiNewsTagLanguagesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HajiNewsTagLanguagesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HajiNewsTagLanguagesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HajiNewsTagLanguagesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
