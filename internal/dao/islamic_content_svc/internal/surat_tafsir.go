// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SuratTafsirDao is the data access object for the table surat_tafsir.
type SuratTafsirDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SuratTafsirColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SuratTafsirColumns defines and stores column names for the table surat_tafsir.
type SuratTafsirColumns struct {
	Id          string //
	TafsirId    string // 注释全局ID
	SurahId     string // 所属章节ID
	AyatNomor   string // 对应经文编号
	Tafsir      string // 注释内容
	CreatedTime string // 创建时间戳(毫秒)
	UpdatedTime string // 修改时间戳(毫秒)
	Wajiz       string // wajiz 解释
}

// suratTafsirColumns holds the columns for the table surat_tafsir.
var suratTafsirColumns = SuratTafsirColumns{
	Id:          "id",
	TafsirId:    "tafsir_id",
	SurahId:     "surah_id",
	AyatNomor:   "ayat_nomor",
	Tafsir:      "tafsir",
	CreatedTime: "created_time",
	UpdatedTime: "updated_time",
	Wajiz:       "wajiz",
}

// NewSuratTafsirDao creates and returns a new DAO object for table data access.
func NewSuratTafsirDao(handlers ...gdb.ModelHandler) *SuratTafsirDao {
	return &SuratTafsirDao{
		group:    "islamic_content_svc",
		table:    "surat_tafsir",
		columns:  suratTafsirColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SuratTafsirDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SuratTafsirDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SuratTafsirDao) Columns() SuratTafsirColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SuratTafsirDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SuratTafsirDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SuratTafsirDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
