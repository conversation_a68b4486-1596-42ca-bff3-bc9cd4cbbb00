// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahDoaPanjangBacaanDao is the data access object for the table umrah_doa_panjang_bacaan.
type UmrahDoaPanjangBacaanDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  UmrahDoaPanjangBacaanColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// UmrahDoaPanjangBacaanColumns defines and stores column names for the table umrah_doa_panjang_bacaan.
type UmrahDoaPanjangBacaanColumns struct {
	Id         string // 主键ID
	DoaId      string // 祈祷文ID，关联umrah_doa_panjang.id
	BacaanNo   string // 诵读序号
	BacaanName string // 诵读名称
	CreateTime string // 创建时间（毫秒时间戳）
	UpdateTime string // 更新时间（毫秒时间戳）
}

// umrahDoaPanjangBacaanColumns holds the columns for the table umrah_doa_panjang_bacaan.
var umrahDoaPanjangBacaanColumns = UmrahDoaPanjangBacaanColumns{
	Id:         "id",
	DoaId:      "doa_id",
	BacaanNo:   "bacaan_no",
	BacaanName: "bacaan_name",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewUmrahDoaPanjangBacaanDao creates and returns a new DAO object for table data access.
func NewUmrahDoaPanjangBacaanDao(handlers ...gdb.ModelHandler) *UmrahDoaPanjangBacaanDao {
	return &UmrahDoaPanjangBacaanDao{
		group:    "islamic_content_svc",
		table:    "umrah_doa_panjang_bacaan",
		columns:  umrahDoaPanjangBacaanColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UmrahDoaPanjangBacaanDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UmrahDoaPanjangBacaanDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UmrahDoaPanjangBacaanDao) Columns() UmrahDoaPanjangBacaanColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UmrahDoaPanjangBacaanDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UmrahDoaPanjangBacaanDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UmrahDoaPanjangBacaanDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
