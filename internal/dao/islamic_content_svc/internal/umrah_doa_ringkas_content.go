// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahDoaRingkasContentDao is the data access object for the table umrah_doa_ringkas_content.
type UmrahDoaRingkasContentDao struct {
	table    string                        // table is the underlying table name of the DAO.
	group    string                        // group is the database configuration group name of the current DAO.
	columns  UmrahDoaRingkasContentColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler            // handlers for customized model modification.
}

// UmrahDoaRingkasContentColumns defines and stores column names for the table umrah_doa_ringkas_content.
type UmrahDoaRingkasContentColumns struct {
	Id             string // 主键ID
	DoaId          string // 祈祷文ID
	ContentOrder   string // 内容排序
	Title          string // 内容标题（可为空）
	MuqattaAt      string // Muqattaʿāt断章字母（有则展示，无不展示）
	ArabicText     string // 阿拉伯文原文
	IndonesianText string // 印尼语翻译
	LatinText      string // 拉丁音译文本
	CreateTime     string // 创建时间（毫秒时间戳）
	UpdateTime     string // 更新时间（毫秒时间戳）
}

// umrahDoaRingkasContentColumns holds the columns for the table umrah_doa_ringkas_content.
var umrahDoaRingkasContentColumns = UmrahDoaRingkasContentColumns{
	Id:             "id",
	DoaId:          "doa_id",
	ContentOrder:   "content_order",
	Title:          "title",
	MuqattaAt:      "muqatta_at",
	ArabicText:     "arabic_text",
	IndonesianText: "indonesian_text",
	LatinText:      "latin_text",
	CreateTime:     "create_time",
	UpdateTime:     "update_time",
}

// NewUmrahDoaRingkasContentDao creates and returns a new DAO object for table data access.
func NewUmrahDoaRingkasContentDao(handlers ...gdb.ModelHandler) *UmrahDoaRingkasContentDao {
	return &UmrahDoaRingkasContentDao{
		group:    "islamic_content_svc",
		table:    "umrah_doa_ringkas_content",
		columns:  umrahDoaRingkasContentColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UmrahDoaRingkasContentDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UmrahDoaRingkasContentDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UmrahDoaRingkasContentDao) Columns() UmrahDoaRingkasContentColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UmrahDoaRingkasContentDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UmrahDoaRingkasContentDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UmrahDoaRingkasContentDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
