// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahLandmarkLanguagesDao is the data access object for the table umrah_landmark_languages.
type UmrahLandmarkLanguagesDao struct {
	table    string                        // table is the underlying table name of the DAO.
	group    string                        // group is the database configuration group name of the current DAO.
	columns  UmrahLandmarkLanguagesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler            // handlers for customized model modification.
}

// UmrahLandmarkLanguagesColumns defines and stores column names for the table umrah_landmark_languages.
type UmrahLandmarkLanguagesColumns struct {
	Id               string // 主键ID
	LandmarkId       string // 地标ID，关联umrah_landmark.id
	LanguageId       string // 语言ID：0-中文，1-英文，2-印尼语
	LandmarkName     string // 地标名称
	Country          string // 国家/地区(后续使用google map api，应该是能获取到的，先使用手动输入的值吧)
	Address          string // 详细地址
	ShortDescription string // 简介
	InformationText  string // 详细介绍
	CreateTime       string // 创建时间（毫秒时间戳）
	UpdateTime       string // 更新时间（毫秒时间戳）
}

// umrahLandmarkLanguagesColumns holds the columns for the table umrah_landmark_languages.
var umrahLandmarkLanguagesColumns = UmrahLandmarkLanguagesColumns{
	Id:               "id",
	LandmarkId:       "landmark_id",
	LanguageId:       "language_id",
	LandmarkName:     "landmark_name",
	Country:          "country",
	Address:          "address",
	ShortDescription: "short_description",
	InformationText:  "information_text",
	CreateTime:       "create_time",
	UpdateTime:       "update_time",
}

// NewUmrahLandmarkLanguagesDao creates and returns a new DAO object for table data access.
func NewUmrahLandmarkLanguagesDao(handlers ...gdb.ModelHandler) *UmrahLandmarkLanguagesDao {
	return &UmrahLandmarkLanguagesDao{
		group:    "islamic_content_svc",
		table:    "umrah_landmark_languages",
		columns:  umrahLandmarkLanguagesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UmrahLandmarkLanguagesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UmrahLandmarkLanguagesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UmrahLandmarkLanguagesDao) Columns() UmrahLandmarkLanguagesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UmrahLandmarkLanguagesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UmrahLandmarkLanguagesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UmrahLandmarkLanguagesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
