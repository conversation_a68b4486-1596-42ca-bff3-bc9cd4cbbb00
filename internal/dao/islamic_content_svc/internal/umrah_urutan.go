// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahUrutanDao is the data access object for the table umrah_urutan.
type UmrahUrutanDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UmrahUrutanColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UmrahUrutanColumns defines and stores column names for the table umrah_urutan.
type UmrahUrutanColumns struct {
	Id         string // 主键ID
	UrutanNo   string // 副朝顺序（数字）
	IconUrl    string // 图标URL
	CreateTime string // 创建时间（毫秒时间戳）
	UpdateTime string // 更新时间（毫秒时间戳）
}

// umrahUrutanColumns holds the columns for the table umrah_urutan.
var umrahUrutanColumns = UmrahUrutanColumns{
	Id:         "id",
	UrutanNo:   "urutan_no",
	IconUrl:    "icon_url",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewUmrahUrutanDao creates and returns a new DAO object for table data access.
func NewUmrahUrutanDao(handlers ...gdb.ModelHandler) *UmrahUrutanDao {
	return &UmrahUrutanDao{
		group:    "islamic_content_svc",
		table:    "umrah_urutan",
		columns:  umrahUrutanColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UmrahUrutanDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UmrahUrutanDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UmrahUrutanDao) Columns() UmrahUrutanColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UmrahUrutanDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UmrahUrutanDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UmrahUrutanDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
