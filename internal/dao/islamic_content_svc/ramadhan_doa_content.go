// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// ramadhanDoaContentDao is the data access object for the table ramadhan_doa_content.
// You can define custom methods on it to extend its functionality as needed.
type ramadhanDoaContentDao struct {
	*internal.RamadhanDoaContentDao
}

var (
	// RamadhanDoaContent is a globally accessible object for table ramadhan_doa_content operations.
	RamadhanDoaContent = ramadhanDoaContentDao{internal.NewRamadhanDoaContentDao()}
)

// Add your custom methods and functionality below.
