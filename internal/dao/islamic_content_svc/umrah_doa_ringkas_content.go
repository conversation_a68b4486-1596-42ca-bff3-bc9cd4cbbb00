// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// umrahDoaRingkasContentDao is the data access object for the table umrah_doa_ringkas_content.
// You can define custom methods on it to extend its functionality as needed.
type umrahDoaRingkasContentDao struct {
	*internal.UmrahDoaRingkasContentDao
}

var (
	// UmrahDoaRingkasContent is a globally accessible object for table umrah_doa_ringkas_content operations.
	UmrahDoaRingkasContent = umrahDoaRingkasContentDao{internal.NewUmrahDoaRingkasContentDao()}
)

// Add your custom methods and functionality below.
