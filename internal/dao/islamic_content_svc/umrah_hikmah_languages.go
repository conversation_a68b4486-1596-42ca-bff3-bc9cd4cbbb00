// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// umrahHikmahLanguagesDao is the data access object for the table umrah_hikmah_languages.
// You can define custom methods on it to extend its functionality as needed.
type umrahHikmahLanguagesDao struct {
	*internal.UmrahHikmahLanguagesDao
}

var (
	// UmrahHikmahLanguages is a globally accessible object for table umrah_hikmah_languages operations.
	UmrahHikmahLanguages = umrahHikmahLanguagesDao{internal.NewUmrahHikmahLanguagesDao()}
)

// Add your custom methods and functionality below.
