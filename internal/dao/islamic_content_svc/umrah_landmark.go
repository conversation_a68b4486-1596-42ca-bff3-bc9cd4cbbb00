// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// umrahLandmarkDao is the data access object for the table umrah_landmark.
// You can define custom methods on it to extend its functionality as needed.
type umrahLandmarkDao struct {
	*internal.UmrahLandmarkDao
}

var (
	// UmrahLandmark is a globally accessible object for table umrah_landmark operations.
	UmrahLandmark = umrahLandmarkDao{internal.NewUmrahLandmarkDao()}
)

// Add your custom methods and functionality below.
