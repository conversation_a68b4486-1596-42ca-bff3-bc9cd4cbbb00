// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// umrahLandmarkLanguagesDao is the data access object for the table umrah_landmark_languages.
// You can define custom methods on it to extend its functionality as needed.
type umrahLandmarkLanguagesDao struct {
	*internal.UmrahLandmarkLanguagesDao
}

var (
	// UmrahLandmarkLanguages is a globally accessible object for table umrah_landmark_languages operations.
	UmrahLandmarkLanguages = umrahLandmarkLanguagesDao{internal.NewUmrahLandmarkLanguagesDao()}
)

// Add your custom methods and functionality below.
