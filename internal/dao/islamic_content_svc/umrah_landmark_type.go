// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// umrahLandmarkTypeDao is the data access object for the table umrah_landmark_type.
// You can define custom methods on it to extend its functionality as needed.
type umrahLandmarkTypeDao struct {
	*internal.UmrahLandmarkTypeDao
}

var (
	// UmrahLandmarkType is a globally accessible object for table umrah_landmark_type operations.
	UmrahLandmarkType = umrahLandmarkTypeDao{internal.NewUmrahLandmarkTypeDao()}
)

// Add your custom methods and functionality below.
