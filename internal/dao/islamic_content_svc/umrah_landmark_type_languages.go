// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// umrahLandmarkTypeLanguagesDao is the data access object for the table umrah_landmark_type_languages.
// You can define custom methods on it to extend its functionality as needed.
type umrahLandmarkTypeLanguagesDao struct {
	*internal.UmrahLandmarkTypeLanguagesDao
}

var (
	// UmrahLandmarkTypeLanguages is a globally accessible object for table umrah_landmark_type_languages operations.
	UmrahLandmarkTypeLanguages = umrahLandmarkTypeLanguagesDao{internal.NewUmrahLandmarkTypeLanguagesDao()}
)

// Add your custom methods and functionality below.
