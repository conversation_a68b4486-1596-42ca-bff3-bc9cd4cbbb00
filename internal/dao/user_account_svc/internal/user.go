// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserDao is the data access object for the table user.
type UserDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UserColumns        // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UserColumns defines and stores column names for the table user.
type UserColumns struct {
	Id                    string //
	AgentId               string // 上级（代理）的id
	AgentCode             string // 注册时填写的代理码（选填）
	Account               string // 账号
	Password              string // 密码
	PayPassword           string // 支付密码
	PasswordModifyTime    string // 登录密码支付密码最近修改时间
	AreaCode              string // 手机国际区号，如：86
	PhoneNum              string // 手机号
	BindPhoneTime         string // 手机号绑定时间
	Email                 string // 邮箱地址
	BindEmailTime         string // 邮箱绑定时间
	BindRealNameTime      string // 真实姓名绑定时间
	VipLevel              string // vip等级
	LevelId               string // 会员层级id
	IsBanned              string // 账号封号状态： 1 正常 2 封号
	IsProhibit            string // 提取状态：1 正常 2 禁提
	IsOnline              string // 是否在线：1是  2 否
	OnlineDuration        string // 在线时长（单位：秒）
	SigninCount           string // 登录次数
	LastSigninTime        string // 最后一次登录时间
	LastSigninIp          string // 最后登录ip
	LastSigninDeviceId    string // 最后登录设备号
	LastSigninAppType     string // 最近登录应用类型（1:android  2: ios，3:h5，4:web，5:其他）
	LastSigninAppVersion  string // 最近登录应用类型版本号
	SignupIp              string // 注册ip
	SignupIpRegion        string // 注册IP地理区域
	SignupDeviceId        string // 注册设备号（设备指纹）
	SignupDeviceOs        string // 注册设备系统（android,ios,windows,mac,...）
	SignupDeviceOsVersion string // 注册设备系统版本号
	SignupDeviceType      string // 注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）
	SignupAppType         string // 应用类型（1:android  2: ios，3:h5，4:web，5:其他）
	SignupAppVersion      string // 注册应用类型版本号
	SignupHost            string // 注册域名(接口域名)
	SignupDomain          string // 注册域名(页面原始域名)
	SignupDomain2         string // 注册域名(页面域名)
	LastSigninLogId       string // 最后一次登录日志id（如果有分表的话，要考虑时间）
	DeviceTokenIos        string // IOS推送token
	DeviceTokenAndroid    string // android推送token(FCM)
	SecurityPassword      string // 安全密码，修改个人绑定信息时要验证
	Version               string // 该记录的版本号
	IsTest                string // 测试账号：  1 是 ，其他值：否
	LimitStartTime        string // 限制登录开始时间
	LimitEndTime          string // 限制登录结束时间
	CreateTime            string // 创建时间（注册时间）
	UpdateTime            string // 更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）
	CreateAccount         string // 创建者账号
	CreateType            string // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
	UpdateAccount         string // 更新者账号
	UpdateType            string // 更新者来源
	InviteCode            string // 邀请码
	TransferCode          string // 转线码
	NoobTaskFinishTime    string // 新手任务完成时间
	DataType              string // 数据类型:1正式数据;2测试数据
	PixelId               string // 像素id
	Source                string // 注册来源( 1直客，2代理，3邀请，4后台）
}

// userColumns holds the columns for the table user.
var userColumns = UserColumns{
	Id:                    "id",
	AgentId:               "agent_id",
	AgentCode:             "agent_code",
	Account:               "account",
	Password:              "password",
	PayPassword:           "pay_password",
	PasswordModifyTime:    "password_modify_time",
	AreaCode:              "area_code",
	PhoneNum:              "phone_num",
	BindPhoneTime:         "bind_phone_time",
	Email:                 "email",
	BindEmailTime:         "bind_email_time",
	BindRealNameTime:      "bind_real_name_time",
	VipLevel:              "vip_level",
	LevelId:               "level_id",
	IsBanned:              "is_banned",
	IsProhibit:            "is_prohibit",
	IsOnline:              "is_online",
	OnlineDuration:        "online_duration",
	SigninCount:           "signin_count",
	LastSigninTime:        "last_signin_time",
	LastSigninIp:          "last_signin_ip",
	LastSigninDeviceId:    "last_signin_device_id",
	LastSigninAppType:     "last_signin_app_type",
	LastSigninAppVersion:  "last_signin_app_version",
	SignupIp:              "signup_ip",
	SignupIpRegion:        "signup_ip_region",
	SignupDeviceId:        "signup_device_id",
	SignupDeviceOs:        "signup_device_os",
	SignupDeviceOsVersion: "signup_device_os_version",
	SignupDeviceType:      "signup_device_type",
	SignupAppType:         "signup_app_type",
	SignupAppVersion:      "signup_app_version",
	SignupHost:            "signup_host",
	SignupDomain:          "signup_domain",
	SignupDomain2:         "signup_domain2",
	LastSigninLogId:       "last_signin_log_id",
	DeviceTokenIos:        "device_token_ios",
	DeviceTokenAndroid:    "device_token_android",
	SecurityPassword:      "security_password",
	Version:               "version",
	IsTest:                "is_test",
	LimitStartTime:        "limit_start_time",
	LimitEndTime:          "limit_end_time",
	CreateTime:            "create_time",
	UpdateTime:            "update_time",
	CreateAccount:         "create_account",
	CreateType:            "create_type",
	UpdateAccount:         "update_account",
	UpdateType:            "update_type",
	InviteCode:            "invite_code",
	TransferCode:          "transfer_code",
	NoobTaskFinishTime:    "noob_task_finish_time",
	DataType:              "data_type",
	PixelId:               "pixel_id",
	Source:                "source",
}

// NewUserDao creates and returns a new DAO object for table data access.
func NewUserDao(handlers ...gdb.ModelHandler) *UserDao {
	return &UserDao{
		group:    "user_account_svc",
		table:    "user",
		columns:  userColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserDao) Columns() UserColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
