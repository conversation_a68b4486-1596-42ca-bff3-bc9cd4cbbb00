// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiNewsTagArticle is the golang structure of table haji_news_tag_article for DAO operations like Where/Data.
type HajiNewsTagArticle struct {
	g.Meta     `orm:"table:haji_news_tag_article, do:true"`
	Id         interface{} // 主键ID
	TagId      interface{} // 标签ID，关联haji_news_tag.id
	ArticleId  interface{} // 文章ID，关联news_article.id
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
