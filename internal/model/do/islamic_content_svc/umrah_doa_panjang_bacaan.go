// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahDoaPanjangBacaan is the golang structure of table umrah_doa_panjang_bacaan for DAO operations like Where/Data.
type UmrahDoaPanjangBacaan struct {
	g.Meta     `orm:"table:umrah_doa_panjang_bacaan, do:true"`
	Id         interface{} // 主键ID
	DoaId      interface{} // 祈祷文ID，关联umrah_doa_panjang.id
	BacaanNo   interface{} // 诵读序号
	BacaanName interface{} // 诵读名称
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
