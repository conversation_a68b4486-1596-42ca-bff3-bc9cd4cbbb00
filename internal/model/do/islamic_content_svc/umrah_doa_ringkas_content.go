// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahDoaRingkasContent is the golang structure of table umrah_doa_ringkas_content for DAO operations like Where/Data.
type UmrahDoaRingkasContent struct {
	g.Meta         `orm:"table:umrah_doa_ringkas_content, do:true"`
	Id             interface{} // 主键ID
	DoaId          interface{} // 祈祷文ID
	ContentOrder   interface{} // 内容排序
	Title          interface{} // 内容标题（可为空）
	MuqattaAt      interface{} // Muqattaʿāt断章字母（有则展示，无不展示）
	ArabicText     interface{} // 阿拉伯文原文
	IndonesianText interface{} // 印尼语翻译
	LatinText      interface{} // 拉丁音译文本
	CreateTime     interface{} // 创建时间（毫秒时间戳）
	UpdateTime     interface{} // 更新时间（毫秒时间戳）
}
