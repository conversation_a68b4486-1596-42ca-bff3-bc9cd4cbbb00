// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahHikmahLanguages is the golang structure of table umrah_hikmah_languages for DAO operations like Where/Data.
type UmrahHikmahLanguages struct {
	g.Meta     `orm:"table:umrah_hikmah_languages, do:true"`
	Id         interface{} // 主键ID
	HikmahId   interface{} // 副朝智慧ID，关联umrah_hikmah.id
	LanguageId interface{} // 语言ID：0-中文，1-英文，2-印尼语
	Title      interface{} // 标题
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
