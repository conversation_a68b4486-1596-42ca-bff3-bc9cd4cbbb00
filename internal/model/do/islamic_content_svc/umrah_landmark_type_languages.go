// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UmrahLandmarkTypeLanguages is the golang structure of table umrah_landmark_type_languages for DAO operations like Where/Data.
type UmrahLandmarkTypeLanguages struct {
	g.Meta     `orm:"table:umrah_landmark_type_languages, do:true"`
	Id         interface{} // 主键ID
	TypeId     interface{} // 地标类型ID，关联umrah_landmark_type.id
	LanguageId interface{} // 语言ID：0-中文，1-英文，2-印尼语
	TypeName   interface{} // 类型名称
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
