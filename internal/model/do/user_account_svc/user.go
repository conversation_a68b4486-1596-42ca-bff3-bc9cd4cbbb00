// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// User is the golang structure of table user for DAO operations like Where/Data.
type User struct {
	g.Meta                `orm:"table:user, do:true"`
	Id                    interface{} //
	AgentId               interface{} // 上级（代理）的id
	AgentCode             interface{} // 注册时填写的代理码（选填）
	Account               interface{} // 账号
	Password              interface{} // 密码
	PayPassword           interface{} // 支付密码
	PasswordModifyTime    interface{} // 登录密码支付密码最近修改时间
	AreaCode              interface{} // 手机国际区号，如：86
	PhoneNum              interface{} // 手机号
	BindPhoneTime         interface{} // 手机号绑定时间
	Email                 interface{} // 邮箱地址
	BindEmailTime         interface{} // 邮箱绑定时间
	BindRealNameTime      interface{} // 真实姓名绑定时间
	VipLevel              interface{} // vip等级
	LevelId               interface{} // 会员层级id
	IsBanned              interface{} // 账号封号状态： 1 正常 2 封号
	IsProhibit            interface{} // 提取状态：1 正常 2 禁提
	IsOnline              interface{} // 是否在线：1是  2 否
	OnlineDuration        interface{} // 在线时长（单位：秒）
	SigninCount           interface{} // 登录次数
	LastSigninTime        interface{} // 最后一次登录时间
	LastSigninIp          interface{} // 最后登录ip
	LastSigninDeviceId    interface{} // 最后登录设备号
	LastSigninAppType     interface{} // 最近登录应用类型（1:android  2: ios，3:h5，4:web，5:其他）
	LastSigninAppVersion  interface{} // 最近登录应用类型版本号
	SignupIp              interface{} // 注册ip
	SignupIpRegion        interface{} // 注册IP地理区域
	SignupDeviceId        interface{} // 注册设备号（设备指纹）
	SignupDeviceOs        interface{} // 注册设备系统（android,ios,windows,mac,...）
	SignupDeviceOsVersion interface{} // 注册设备系统版本号
	SignupDeviceType      interface{} // 注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）
	SignupAppType         interface{} // 应用类型（1:android  2: ios，3:h5，4:web，5:其他）
	SignupAppVersion      interface{} // 注册应用类型版本号
	SignupHost            interface{} // 注册域名(接口域名)
	SignupDomain          interface{} // 注册域名(页面原始域名)
	SignupDomain2         interface{} // 注册域名(页面域名)
	LastSigninLogId       interface{} // 最后一次登录日志id（如果有分表的话，要考虑时间）
	DeviceTokenIos        interface{} // IOS推送token
	DeviceTokenAndroid    interface{} // android推送token(FCM)
	SecurityPassword      interface{} // 安全密码，修改个人绑定信息时要验证
	Version               interface{} // 该记录的版本号
	IsTest                interface{} // 测试账号：  1 是 ，其他值：否
	LimitStartTime        interface{} // 限制登录开始时间
	LimitEndTime          interface{} // 限制登录结束时间
	CreateTime            interface{} // 创建时间（注册时间）
	UpdateTime            interface{} // 更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）
	CreateAccount         interface{} // 创建者账号
	CreateType            interface{} // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
	UpdateAccount         interface{} // 更新者账号
	UpdateType            interface{} // 更新者来源
	InviteCode            interface{} // 邀请码
	TransferCode          interface{} // 转线码
	NoobTaskFinishTime    interface{} // 新手任务完成时间
	DataType              interface{} // 数据类型:1正式数据;2测试数据
	PixelId               interface{} // 像素id
	Source                interface{} // 注册来源( 1直客，2代理，3邀请，4后台）
}
