// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// HajiNewsTagArticle is the golang structure for table haji_news_tag_article.
type HajiNewsTagArticle struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`
	TagId      uint64 `json:"tagId"      orm:"tag_id"      description:"标签ID，关联haji_news_tag.id"`
	ArticleId  uint   `json:"articleId"  orm:"article_id"  description:"文章ID，关联news_article.id"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间（毫秒时间戳）"`
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间（毫秒时间戳）"`
}
