// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// UmrahDoaPanjangBacaan is the golang structure for table umrah_doa_panjang_bacaan.
type UmrahDoaPanjangBacaan struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`
	DoaId      uint64 `json:"doaId"      orm:"doa_id"      description:"祈祷文ID，关联umrah_doa_panjang.id"`
	BacaanNo   int    `json:"bacaanNo"   orm:"bacaan_no"   description:"诵读序号"`
	BacaanName string `json:"bacaanName" orm:"bacaan_name" description:"诵读名称"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间（毫秒时间戳）"`
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间（毫秒时间戳）"`
}
