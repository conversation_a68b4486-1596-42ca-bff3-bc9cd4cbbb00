// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// UmrahHikmahLanguages is the golang structure for table umrah_hikmah_languages.
type UmrahHikmahLanguages struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`
	HikmahId   uint64 `json:"hikmahId"   orm:"hikmah_id"   description:"副朝智慧ID，关联umrah_hikmah.id"`
	LanguageId uint   `json:"languageId" orm:"language_id" description:"语言ID：0-中文，1-英文，2-印尼语"`
	Title      string `json:"title"      orm:"title"       description:"标题"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间（毫秒时间戳）"`
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间（毫秒时间戳）"`
}
