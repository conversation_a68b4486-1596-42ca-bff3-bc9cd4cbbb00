// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// UmrahLandmarkType is the golang structure for table umrah_landmark_type.
type UmrahLandmarkType struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`
	IconUrl    string `json:"iconUrl"    orm:"icon_url"    description:"图标路径"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间（毫秒时间戳）"`
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间（毫秒时间戳）"`
}
