// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// UmrahUrutanContent is the golang structure for table umrah_urutan_content.
type UmrahUrutanContent struct {
	Id            uint64 `json:"id"            orm:"id"             description:"主键ID"`
	UrutanId      uint64 `json:"urutanId"      orm:"urutan_id"      description:"副朝顺序ID，关联umrah_urutan.id"`
	LanguageId    uint   `json:"languageId"    orm:"language_id"    description:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	UrutanName    string `json:"urutanName"    orm:"urutan_name"    description:"副朝仪式名称（最多60个字符）"`
	UrutanTime    string `json:"urutanTime"    orm:"urutan_time"    description:"仪式时间"`
	UrutanContent string `json:"urutanContent" orm:"urutan_content" description:"仪式内容描述（富文本）"`
	CreateTime    uint64 `json:"createTime"    orm:"create_time"    description:"创建时间（毫秒时间戳）"`
	UpdateTime    uint64 `json:"updateTime"    orm:"update_time"    description:"更新时间（毫秒时间戳）"`
}
