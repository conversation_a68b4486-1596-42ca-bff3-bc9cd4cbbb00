// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// User is the golang structure for table user.
type User struct {
	Id                    uint   `json:"id"                    orm:"id"                       description:""`
	AgentId               uint   `json:"agentId"               orm:"agent_id"                 description:"上级（代理）的id"`
	AgentCode             string `json:"agentCode"             orm:"agent_code"               description:"注册时填写的代理码（选填）"`
	Account               string `json:"account"               orm:"account"                  description:"账号"`
	Password              string `json:"password"              orm:"password"                 description:"密码"`
	PayPassword           string `json:"payPassword"           orm:"pay_password"             description:"支付密码"`
	PasswordModifyTime    int64  `json:"passwordModifyTime"    orm:"password_modify_time"     description:"登录密码支付密码最近修改时间"`
	AreaCode              string `json:"areaCode"              orm:"area_code"                description:"手机国际区号，如：86"`
	PhoneNum              string `json:"phoneNum"              orm:"phone_num"                description:"手机号"`
	BindPhoneTime         int64  `json:"bindPhoneTime"         orm:"bind_phone_time"          description:"手机号绑定时间"`
	Email                 string `json:"email"                 orm:"email"                    description:"邮箱地址"`
	BindEmailTime         int64  `json:"bindEmailTime"         orm:"bind_email_time"          description:"邮箱绑定时间"`
	BindRealNameTime      int64  `json:"bindRealNameTime"      orm:"bind_real_name_time"      description:"真实姓名绑定时间"`
	VipLevel              int    `json:"vipLevel"              orm:"vip_level"                description:"vip等级"`
	LevelId               uint   `json:"levelId"               orm:"level_id"                 description:"会员层级id"`
	IsBanned              int    `json:"isBanned"              orm:"is_banned"                description:"账号封号状态： 1 正常 2 封号"`
	IsProhibit            int    `json:"isProhibit"            orm:"is_prohibit"              description:"提取状态：1 正常 2 禁提"`
	IsOnline              int    `json:"isOnline"              orm:"is_online"                description:"是否在线：1是  2 否"`
	OnlineDuration        int64  `json:"onlineDuration"        orm:"online_duration"          description:"在线时长（单位：秒）"`
	SigninCount           int    `json:"signinCount"           orm:"signin_count"             description:"登录次数"`
	LastSigninTime        int64  `json:"lastSigninTime"        orm:"last_signin_time"         description:"最后一次登录时间"`
	LastSigninIp          string `json:"lastSigninIp"          orm:"last_signin_ip"           description:"最后登录ip"`
	LastSigninDeviceId    string `json:"lastSigninDeviceId"    orm:"last_signin_device_id"    description:"最后登录设备号"`
	LastSigninAppType     int    `json:"lastSigninAppType"     orm:"last_signin_app_type"     description:"最近登录应用类型（1:android  2: ios，3:h5，4:web，5:其他）"`
	LastSigninAppVersion  string `json:"lastSigninAppVersion"  orm:"last_signin_app_version"  description:"最近登录应用类型版本号"`
	SignupIp              string `json:"signupIp"              orm:"signup_ip"                description:"注册ip"`
	SignupIpRegion        string `json:"signupIpRegion"        orm:"signup_ip_region"         description:"注册IP地理区域"`
	SignupDeviceId        string `json:"signupDeviceId"        orm:"signup_device_id"         description:"注册设备号（设备指纹）"`
	SignupDeviceOs        string `json:"signupDeviceOs"        orm:"signup_device_os"         description:"注册设备系统（android,ios,windows,mac,...）"`
	SignupDeviceOsVersion string `json:"signupDeviceOsVersion" orm:"signup_device_os_version" description:"注册设备系统版本号"`
	SignupDeviceType      int    `json:"signupDeviceType"      orm:"signup_device_type"       description:"注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）"`
	SignupAppType         int    `json:"signupAppType"         orm:"signup_app_type"          description:"应用类型（1:android  2: ios，3:h5，4:web，5:其他）"`
	SignupAppVersion      string `json:"signupAppVersion"      orm:"signup_app_version"       description:"注册应用类型版本号"`
	SignupHost            string `json:"signupHost"            orm:"signup_host"              description:"注册域名(接口域名)"`
	SignupDomain          string `json:"signupDomain"          orm:"signup_domain"            description:"注册域名(页面原始域名)"`
	SignupDomain2         string `json:"signupDomain2"         orm:"signup_domain2"           description:"注册域名(页面域名)"`
	LastSigninLogId       int    `json:"lastSigninLogId"       orm:"last_signin_log_id"       description:"最后一次登录日志id（如果有分表的话，要考虑时间）"`
	DeviceTokenIos        string `json:"deviceTokenIos"        orm:"device_token_ios"         description:"IOS推送token"`
	DeviceTokenAndroid    string `json:"deviceTokenAndroid"    orm:"device_token_android"     description:"android推送token(FCM)"`
	SecurityPassword      string `json:"securityPassword"      orm:"security_password"        description:"安全密码，修改个人绑定信息时要验证"`
	Version               int    `json:"version"               orm:"version"                  description:"该记录的版本号"`
	IsTest                int    `json:"isTest"                orm:"is_test"                  description:"测试账号：  1 是 ，其他值：否"`
	LimitStartTime        int64  `json:"limitStartTime"        orm:"limit_start_time"         description:"限制登录开始时间"`
	LimitEndTime          int64  `json:"limitEndTime"          orm:"limit_end_time"           description:"限制登录结束时间"`
	CreateTime            int64  `json:"createTime"            orm:"create_time"              description:"创建时间（注册时间）"`
	UpdateTime            int64  `json:"updateTime"            orm:"update_time"              description:"更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）"`
	CreateAccount         string `json:"createAccount"         orm:"create_account"           description:"创建者账号"`
	CreateType            int    `json:"createType"            orm:"create_type"              description:"创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）"`
	UpdateAccount         string `json:"updateAccount"         orm:"update_account"           description:"更新者账号"`
	UpdateType            int    `json:"updateType"            orm:"update_type"              description:"更新者来源"`
	InviteCode            string `json:"inviteCode"            orm:"invite_code"              description:"邀请码"`
	TransferCode          string `json:"transferCode"          orm:"transfer_code"            description:"转线码"`
	NoobTaskFinishTime    int64  `json:"noobTaskFinishTime"    orm:"noob_task_finish_time"    description:"新手任务完成时间"`
	DataType              int    `json:"dataType"              orm:"data_type"                description:"数据类型:1正式数据;2测试数据"`
	PixelId               string `json:"pixelId"               orm:"pixel_id"                 description:"像素id"`
	Source                int    `json:"source"                orm:"source"                   description:"注册来源( 1直客，2代理，3邀请，4后台）"`
}
