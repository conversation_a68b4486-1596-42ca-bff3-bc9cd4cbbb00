// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IUmrahLandmark interface {
		// TypeList 地标类型列表
		TypeList(ctx context.Context, req *v1.UmrahLandmarkTypeListReq) (out *v1.UmrahLandmarkTypeListRes, err error)
		// TypeAdd 新增地标类型
		TypeAdd(ctx context.Context, req *v1.UmrahLandmarkTypeCreateReq) (res *v1.UmrahLandmarkTypeCreateRes, err error)
		// TypeEdit 编辑地标类型
		TypeEdit(ctx context.Context, req *v1.UmrahLandmarkTypeEditReq) (res *v1.UmrahLandmarkTypeEditRes, err error)
		// TypeOne 获取单个地标类型
		TypeOne(ctx context.Context, req *v1.UmrahLandmarkTypeOneReq) (res *v1.UmrahLandmarkTypeOneRes, err error)
		// TypeDelete 删除地标类型
		TypeDelete(ctx context.Context, req *v1.UmrahLandmarkTypeDeleteReq) (res *v1.UmrahLandmarkTypeDeleteRes, err error)
		// List 地标列表
		List(ctx context.Context, req *v1.UmrahLandmarkListReq) (res *v1.UmrahLandmarkListRes, err error)
		// Add 新增地标
		Add(ctx context.Context, req *v1.UmrahLandmarkCreateReq) (res *v1.UmrahLandmarkCreateRes, err error)
		// Edit 编辑地标
		Edit(ctx context.Context, req *v1.UmrahLandmarkEditReq) (res *v1.UmrahLandmarkEditRes, err error)
		// One 获取单个地标
		One(ctx context.Context, req *v1.UmrahLandmarkOneReq) (res *v1.UmrahLandmarkOneRes, err error)
		// Delete 删除地标
		Delete(ctx context.Context, req *v1.UmrahLandmarkDeleteReq) (res *v1.UmrahLandmarkDeleteRes, err error)
	}
)

var (
	localUmrahLandmark IUmrahLandmark
)

func UmrahLandmark() IUmrahLandmark {
	if localUmrahLandmark == nil {
		panic("implement not found for interface IUmrahLandmark, forgot register?")
	}
	return localUmrahLandmark
}

func RegisterUmrahLandmark(i IUmrahLandmark) {
	localUmrahLandmark = i
}
