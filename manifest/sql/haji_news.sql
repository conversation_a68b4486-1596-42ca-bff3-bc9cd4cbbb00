create table islamic_content_svc.haji_news_tag
(
    id            bigint unsigned auto_increment comment '主键ID'
        primary key,
    article_count int unsigned      default '0' not null comment '关联文章数（不一定使用这个字段，可由关联表实时计算）',
    sort_order    smallint unsigned default '0' not null comment '排序值，数字越小排序越靠前',
    create_time   bigint unsigned   default '0' not null comment '创建时间（毫秒时间戳）',
    update_time   bigint unsigned   default '0' not null comment '更新时间（毫秒时间戳）'
)
    comment '朝觐新闻标签主表';

create index idx_create_time
    on islamic_content_svc.haji_news_tag (create_time)
    comment '创建时间索引';

create index idx_sort_order
    on islamic_content_svc.haji_news_tag (sort_order)
    comment '排序索引';

create table islamic_content_svc.haji_news_tag_languages
(
    id          bigint unsigned auto_increment comment '主键ID'
        primary key,
    tag_id      bigint unsigned              not null comment '标签ID，关联haji_news_tag.id',
    language_id tinyint unsigned default '0' not null comment '语言ID：0-中文，1-英文，2-印尼语',
    tag_name    varchar(200)                 not null comment '标签名称',
    create_time bigint unsigned  default '0' not null comment '创建时间（毫秒时间戳）',
    update_time bigint unsigned  default '0' not null comment '更新时间（毫秒时间戳）',
    constraint uk_tag_language
        unique (tag_id, language_id) comment '标签ID和语言唯一索引',
    constraint haji_news_tag_languages_ibfk_1
        foreign key (tag_id) references islamic_content_svc.haji_news_tag (id)
            on update cascade on delete cascade
)
    comment '朝觐新闻标签多语言表';

create index idx_language_id
    on islamic_content_svc.haji_news_tag_languages (language_id)
    comment '语言ID索引';

create index idx_tag_id
    on islamic_content_svc.haji_news_tag_languages (tag_id)
    comment '标签ID索引';

create table islamic_content_svc.haji_news_tag_article
(
    id          bigint unsigned auto_increment comment '主键ID'
        primary key,
    tag_id      bigint unsigned             not null comment '标签ID，关联haji_news_tag.id',
    article_id  int unsigned                not null comment '文章ID，关联news_article.id',
    create_time bigint unsigned default '0' not null comment '创建时间（毫秒时间戳）',
    update_time bigint unsigned default '0' not null comment '更新时间（毫秒时间戳）',
    constraint uk_tag_article
        unique (tag_id, article_id) comment '同一文章在同一标签下唯一',
    constraint haji_news_tag_article_ibfk_1
        foreign key (tag_id) references islamic_content_svc.haji_news_tag (id)
            on update cascade on delete cascade,
    constraint haji_news_tag_article_ibfk_2
        foreign key (article_id) references islamic_content_svc.news_article (id)
            on update cascade on delete cascade
)
    comment '朝觐新闻标签与文章关联表';

create index idx_article_id
    on islamic_content_svc.haji_news_tag_article (article_id)
    comment '文章ID索引';

create index idx_tag_id
    on islamic_content_svc.haji_news_tag_article (tag_id)
    comment '标签ID索引';

